{"common": {"loading": "Just a moment, loading...", "error": "Oops! Something went wrong.", "tryAgain": "Please try again", "search": "Find Anything", "home": "Your Home", "latest": "What's New", "login": "Sign In", "logout": "Sign Out", "cancel": "Cancel", "submit": "Submit", "save": "Save"}, "navigation": {"home": "Home", "latest": "Latest Releases", "ranking": "Rankings", "search": "Search", "searchManga": "Looking for manga?", "profile": "Your Profile", "myFavorites": "Your Favorites", "history": "Reading History", "notifications": "Notifications", "settings": "Settings", "genres": "Genres", "viewAllGenres": "See All Genres", "loading": "Loading..."}, "manga": {"hotManga": "Popular Right Now", "latestManga": "Freshly Added Manga", "latestUpdates": "Latest Updates", "recommendedManga": "Manga We Think You'll Love", "viewMoreManga": "View More Manga", "rating": "Rating", "ratings": "Ratings", "views": "Views", "favorites": "Favorites", "status": "Status", "ongoing": "Ongoing", "completed": "Completed", "readNow": "Start Reading", "addToFavorites": "Add to Your Favorites", "removeFromFavorites": "Remove from Favorites", "updating": "Updating...", "chapters": "chapters", "chaptersCount": "{count} chapters", "noChaptersFound": "No chapters found matching \"{searchTerm}\". Sorry about that!", "chaptersList": "Chapter List", "alternativeTitles": "Other Titles", "author": "Author", "artist": "Artist", "published": "Published", "serialization": "Serialization", "updated": "Last Updated", "readFirstChapter": "Read the First Chapter", "readLatestChapter": "Read the Latest Chapter", "searchChapters": "Search chapters", "newestFirst": "Newest First", "oldestFirst": "Oldest First", "synopsis": "Synopsis", "information": "Information", "showMore": "Show More", "showLess": "Show Less", "youMayAlsoLike": "You May Also Like", "details": "Details", "authorLabel": "Author:", "artistLabel": "Artist:", "statusLabel": "Status:", "publishedLabel": "Published:", "serializationLabel": "Serialization:", "updatedLabel": "Updated:", "showAllChapters": "Show all chapters", "loginToRate": "Login to rate this manga", "ratingSubmitted": "Rating submitted!"}, "search": {"placeholder": "Search for your next manga...", "placeholderSimple": "Search manga...", "searching": "Searching for you...", "noResults": "No results found. Maybe try a different search?", "searchFor": "Searching for", "searchResultsFor": "Search results for:", "results": "results", "recentSearches": "Your Recent Searches", "popularSearches": "Popular Searches", "popularManga": "Popular Manga", "suggestions": "Suggestions for you:", "applyFilters": "Apply Filters", "noResultsFound": "No results found for", "tryDifferentSearch": "Try a different search term or adjusting your filters.", "enterSearchTerm": "Enter a search term to find manga.", "errorLoadingResults": "Error Loading Search Results", "checkConnection": "Please try again with a different search term or check your connection."}, "filterSort": {"sort": "Sort By", "filter": "Filter", "filterManga": "<PERSON><PERSON>", "applyFilters": "Apply Filters", "status": "Status", "genres": "Genres", "genre": "Genre", "allGenres": "All Genres", "allStatus": "All Status", "loadingGenres": "Loading genres...", "reset": "Reset Filters", "selectStatus": "Select a status", "filterResults": "Filter Results", "statusOptions": {"all": "All", "ongoing": "Ongoing", "completed": "Completed", "hiatus": "<PERSON><PERSON>"}, "sortOptions": {"relevance": "Relevance", "latest": "Latest Update", "newest": "Newest Added", "popular": "Most Popular", "views": "Most Views", "title": "Title (A-Z)", "rating": "Highest Rating"}}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "emailOrUsername": "Email or Username", "password": "Password", "confirmPassword": "Confirm Password", "loginSuccess": "You're all logged in! Welcome back!", "loginError": "Hmm, looks like your email or password isn't quite right.", "loggingIn": "Logging you in...", "enterCredentials": "Please enter your details to access your account.", "logout": "Sign out", "logoutSuccess": "Logged out successfully", "logoutError": "Failed to log out", "signingOut": "Signing out..."}, "errors": {"failedToLoad": "Couldn't load that for you.", "tryAgainLater": "Please try again a little later.", "somethingWentWrong": "Something went wrong on our end.", "networkError": "Looks like a network hiccup.", "invalidEmail": "Please enter a valid email address.", "passwordRequired": "Don't forget your password!", "emailOrUsernameRequired": "Please enter your email or username."}, "notFound": {"title": "Page Not Found", "heading": "404 - Manga Not Found", "description": "Oops! The manga page you're looking for seems to have vanished into another dimension. So sorry!", "subDescription": "Don't worry, even the best manga readers get lost sometimes. Let's get you back on track!", "homeButton": "Back to Home", "browseButton": "<PERSON><PERSON>e <PERSON>", "suggestions": "Here are some suggestions for you:", "checkLatest": "Check out the latest manga releases", "exploreGenres": "Explore different manga genres", "viewFavorites": "View your favorite manga", "contactSupport": "Contact support if you think this is an error."}, "footer": {"home": "Home", "privacy": "Privacy Policy", "terms": "Terms of Service", "dmca": "DMCA", "contact": "Contact", "allRightsReserved": "All rights reserved", "siteInfo": {"title": "Manga Next", "description": "Your ultimate destination for reading manga online for free. Discover thousands of manga series with the latest updates.", "tagline": "Read Manga Online for Free"}, "navigation": {"title": "Navigation", "browse": "Browse", "popular": "Popular", "latest": "Latest", "search": "Search", "genres": "Genres"}, "legal": {"title": "Legal", "about": "About Us", "help": "Help & Support"}, "social": {"title": "Connect", "followUs": "Follow us on social media"}, "stats": {"title": "Statistics", "totalManga": "Total Manga", "totalChapters": "Total Chapters", "totalUsers": "Total Users", "totalComments": "Total Comments"}, "backToTop": "Back to Top", "copyright": "© {year} {siteName}. {allRightsReserved}"}, "format": {"timeAgo": {"minutesAgo": "{minutes} minutes ago", "hoursAgo": "{hours} hours ago", "yesterday": "Yesterday", "daysAgo": "{days} days ago", "weekAgo": "1 week ago", "weeksAgo": "{weeks} weeks ago"}, "months": {"short": {"0": "Jan", "1": "Feb", "2": "Mar", "3": "Apr", "4": "May", "5": "Jun", "6": "Jul", "7": "Aug", "8": "Sep", "9": "Oct", "10": "Nov", "11": "Dec"}, "long": {"0": "January", "1": "February", "2": "March", "3": "April", "4": "May", "5": "June", "6": "July", "7": "August", "8": "September", "9": "October", "10": "November", "11": "December"}}, "numbers": {"thousand": "K", "million": "M", "billion": "B"}}, "comments": {"noRecentComments": "No comment yet", "chapter": "Chapter", "title": "Comments", "chapterComments": "Chapter Comments", "allComments": "All Comments", "writeComment": "Write a comment...", "writeReply": "Write a reply...", "shareThoughts": "Share your thoughts about this manga...", "noCommentsYet": "No comments yet. Be the first to share your thoughts!", "signInToComment": "Please {signInLink} to post comments.", "signIn": "sign in", "sortBy": {"newest": "Newest", "oldest": "Oldest", "mostLiked": "Most Liked"}, "actions": {"reply": "Reply", "edit": "Edit", "delete": "Delete", "report": "Report", "like": "Like", "dislike": "Dislike", "cancel": "Cancel", "save": "Save", "update": "Update", "postComment": "Post Comment", "posting": "Posting...", "updating": "Updating...", "loadMore": "Load More Comments", "loadingMore": "Loading More Comments...", "showReplies": "Show {count} more replies", "hideReplies": "Hide {count} more replies"}, "form": {"placeholder": {"comment": "Share your thoughts about this manga...", "reply": "Write a reply..."}, "validation": {"required": "Comment cannot be empty", "maxLength": "Comment cannot exceed {max} characters", "whitespace": "Comment cannot contain only whitespace", "inappropriateContent": "Content contains inappropriate language: {words}", "excessiveCaps": "Content contains too many capital letters", "excessiveRepetition": "Content contains too many repeated words", "tooManyLinks": "Content contains too many links"}, "characterCount": "{current}/{max}", "guidelines": {"title": "Community Guidelines", "respectful": "Please be respectful and constructive in your comments", "spoilers": "Avoid spoilers without proper warnings", "noSpam": "No spam or advertisements", "stayOnTopic": "Stay on topic with your comments"}}, "status": {"pending": "Pending", "flagged": "Flagged", "edited": "(edited)"}, "badges": {"admin": "Admin", "moderator": "Moderator", "chapter": "Chapter {number}"}, "messages": {"commentPosted": "Comment posted successfully!", "commentUpdated": "Comment updated successfully!", "commentDeleted": "Comment deleted successfully!", "commentPending": "Comment has been sent for review", "signInToLike": "Please sign in to like the comment", "failedToLoad": "Failed to load comments", "failedToPost": "Failed to post comment", "failedToUpdate": "Failed to update comment", "failedToDelete": "Failed to delete comment", "failedToLike": "Failed to like comment", "failedToLoadMore": "Failed to load more comments", "tryAgain": "Try again"}, "deleteDialog": {"title": "Delete Comment", "description": "Are you sure you want to delete this comment? This action cannot be undone.", "cancel": "Cancel", "delete": "Delete"}, "reportDialog": {"title": "Report Comment", "description": "Help us maintain a safe and respectful community by reporting inappropriate content.", "reasonLabel": "Reason for Reporting", "detailsLabel": "Additional Details", "detailsPlaceholder": "Please provide more details about the reason for reporting this comment...", "cancel": "Cancel", "submit": "Report Comment", "submitting": "Reporting...", "reasons": {"spam": "Spam or advertisement", "harassment": "Harassment", "inappropriate_content": "Inappropriate or offensive content", "spoilers": "Spoilers without warning", "off_topic": "Off-topic comment", "other": "Other (please specify)"}, "note": "Reporting inappropriately can result in account restrictions. Please only report content that actually violates our community guidelines.", "success": "Comment reported successfully. Thank you for helping us maintain a safe and respectful community.", "error": "Failed to report comment"}}, "sidebar": {"recentComments": "Recent Comments", "readingHistory": "Reading History", "recommendedManga": "Recommended Manga", "rankings": "Manga Rankings", "rankingsData": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "trending": "Hot", "errorLoading": "Failed to load rankings", "noRankings": "No rankings available", "viewAllRankings": "View All Rankings"}, "viewAllRankings": "View All Rankings", "noRankingsAvailable": "No rankings available yet", "noCommentsYet": "No comments yet."}, "rankings": {"title": "Manga Rankings", "subtitle": "Discover the most popular manga across different categories and time periods", "categories": {"mostViewed": "Most Viewed", "highestRated": "Highest Rated", "mostBookmarked": "Most Bookmarked", "trending": "Trending Now", "most_viewed": "Most Viewed", "most_bookmarked": "Most Bookmarked", "highest_rated": "Highest Rated"}, "categoryDescriptions": {"mostViewed": "Based on view counts", "highestRated": "Based on user ratings", "mostBookmarked": "Based on favorites count", "trending": "Rising in popularity"}, "periods": {"daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "allTime": "All Time", "all_time": "All Time"}, "stats": {"views": "views", "rating": "rating", "bookmarks": "bookmarks", "rank": "Rank #{rank}", "topRanked": "Top Ranked", "newEntry": "New Entry", "rising": "Rising"}, "filters": {"category": "Category", "timePeriod": "Time Period", "selectCategory": "Choose what type of rankings you want to see", "selectPeriod": "Select the time range for the rankings", "applyFilters": "Apply Filters", "resetFilters": "Reset Filters"}, "pagination": {"showingResults": "Showing {start}-{end} of {total} manga", "loadMore": "Load More Rankings", "previousPage": "Previous Page", "nextPage": "Next Page", "goToPage": "Go to page {page}"}, "empty": {"title": "No Rankings Available", "description": "There are no manga rankings available for this category and time period.", "suggestion": "Try selecting a different category or time period."}, "error": {"title": "Failed to Load Rankings", "description": "We couldn't load the manga rankings at this time.", "retry": "Try Again", "goBack": "Go Back"}, "loading": {"title": "Loading Rankings...", "description": "Please wait while we fetch the latest manga rankings."}, "actions": {"viewDetails": "View Details", "readNow": "Read Now", "addToFavorites": "Add to Favorites", "viewAllRankings": "View All Rankings", "backToRankings": "Back to Rankings"}, "mobile": {"showStats": "Show Stats", "hideStats": "Hide Stats", "moreInfo": "More Info", "quickActions": "Quick Actions"}, "seo": {"metaTitle": {"default": "Manga Rankings - Top Manga by Views, Ratings & Bookmarks", "mostViewed": "{period} Most Viewed Manga Rankings", "highestRated": "{period} Highest Rated Manga Rankings", "mostBookmarked": "{period} Most Bookmarked Manga Rankings", "trending": "{period} Trending Manga Rankings"}, "metaDescription": {"default": "Discover the most popular manga with our comprehensive rankings. Find top manga by views, ratings, bookmarks, and trending status across daily, weekly, monthly, and all-time periods.", "mostViewed": "Explore the {period} most viewed manga rankings. Discover what manga readers are enjoying most with real-time view statistics and trending data.", "highestRated": "Find the {period} highest rated manga based on user reviews and ratings. Discover critically acclaimed manga with the best reader scores.", "mostBookmarked": "Browse the {period} most bookmarked manga. See what manga readers are saving to their favorites and adding to their reading lists.", "trending": "Check out the {period} trending manga that are gaining popularity. Discover rising stars and hot new releases in the manga world."}}}, "readingHistory": {"removeFromHistory": "Remove from history", "noHistoryYet": "No reading history yet.", "exploreToRead": "Explore manga to start reading!"}, "viewStats": {"title": "View Statistics for {type}", "overview": "Overview", "trends": "Trends", "totalViews": "Total Views", "dailyViews": "Daily Views", "weeklyViews": "Weekly Views", "monthlyViews": "Monthly Views", "today": "today", "thisWeek": "this week", "thisMonth": "this month", "trendsComingSoon": "Trends coming soon", "trendsDescription": "Historical view trends will be available here"}, "pwa": {"install": {"title": "Install Manga App", "description": "Get the full app experience with offline reading and faster loading.", "ios-instruction": "Tap the Share button and select 'Add to Home Screen' to install.", "button": "Install", "dismiss": "Not now"}, "offline": {"title": "You're offline", "description": "Some features may not be available while offline.", "retry": "Try again"}, "update": {"title": "App Update Available", "description": "A new version of the app is available.", "button": "Update", "dismiss": "Later"}}, "chapterReport": {"reportIssue": "Report Issue", "reportChapterIssue": "Report Chapter Issue", "reportChapterIssueTitle": "Report Chapter Issue", "reportChapterIssueDescription": "Report technical issues with this chapter. Your feedback helps us maintain content quality.", "issueType": "Issue Type", "issueTypeRequired": "Issue Type *", "selectIssueType": "Select the type of issue", "additionalDetails": "Additional Details", "additionalDetailsRequired": "Additional Details *", "additionalDetailsOptional": "Provide additional context about the issue (optional)...", "additionalDetailsPlaceholder": "Please describe the issue in detail...", "cancel": "Cancel", "submitReport": "Submit Report", "submitting": "Submitting...", "reportSuccess": "Chapter reported successfully. Thank you for helping us improve the content quality.", "reportError": "Failed to report chapter", "loginRequired": "Please sign in to report chapter issues", "wordLimitExceeded": "Details cannot exceed 50 words", "characterLimitExceeded": "Details cannot exceed 50 words (approximately 300 characters)", "words": "words", "reasons": {"broken_images": "Broken or missing images", "missing_pages": "Missing pages", "wrong_order": "Pages in wrong order", "duplicate_pages": "Duplicate pages", "poor_quality": "Poor image quality", "wrong_chapter": "Wrong chapter content", "corrupted_content": "Corrupted or unreadable content", "other": "Other issue"}}, "notifications": {"title": "Notifications", "noNotifications": "No notifications yet", "markAllRead": "Mark all as read", "markAsRead": "<PERSON> as read", "newChapter": "New Chapter", "settings": "Notification Settings", "inAppNotifications": "In-app notifications", "newChapterAlerts": "New chapter alerts", "saveSettings": "Save Settings", "settingsUpdated": "Notification settings updated successfully", "failedToLoad": "Failed to load notifications", "failedToUpdate": "Failed to update settings", "loginRequired": "Please log in to view notifications", "viewAllNotifications": "View All Notifications", "saving": "Saving...", "howNotificationsWork": "How notifications work", "notificationInfo": {"favoritesOnly": "You'll only receive notifications for manga you've added to favorites", "newChapters": "Notifications appear when new chapters are published", "bellIcon": "You can view all notifications from the bell icon in the header", "autoMarkRead": "Notifications are automatically marked as read when you click on them"}, "settingsDescription": "Manage your notification preferences and stay updated with your favorite manga.", "inAppDescription": "Receive notifications within the application", "newChapterDescription": "Get notified when your favorited manga releases new chapters", "refresh": "Refresh", "settingsButton": "Settings", "loadingNotifications": "Loading notifications...", "noNotificationsDescription": "You'll receive notifications here when your favorited manga get new chapters.", "browseManga": "<PERSON><PERSON>e <PERSON>", "loading": "Loading...", "loadMore": "Load More", "showingNotifications": "Showing {count} of {total} notifications", "types": {"new_chapter": "New Chapter", "system": "System"}, "messages": {"newChapterAvailable": "New Chapter Available!", "chapterReleased": "Chapter {number} of \"{title}\" has been released."}}, "reader": {"endOfChapter": "End of chapter {number}", "previousChapter": "Previous Chapter", "nextChapter": "Next Chapter", "pageAlt": "Page {number}"}, "staticPages": {"about": {"title": "About Us", "subtitle": "Learn more about Manga Next", "mission": {"title": "Our Mission", "content": "At Manga Next, we're passionate about bringing the best manga reading experience to fans worldwide. Our mission is to provide a comprehensive, user-friendly platform where manga enthusiasts can discover, read, and enjoy their favorite series."}, "features": {"title": "What We Offer", "items": ["Extensive manga library with thousands of titles", "Regular updates with the latest chapters", "User-friendly reading interface", "Personalized recommendations", "Community features and discussions", "Multi-language support"]}, "team": {"title": "Our Team", "content": "We're a dedicated team of manga lovers, developers, and designers working together to create the ultimate manga reading platform."}}, "help": {"title": "Help & Support", "subtitle": "Find answers to common questions", "faq": {"title": "Frequently Asked Questions", "items": [{"question": "How do I create an account?", "answer": "Click on the 'Login' button in the header and select 'Register' to create a new account."}, {"question": "How do I add manga to my favorites?", "answer": "Visit any manga page and click the heart icon to add it to your favorites list."}, {"question": "How do I report issues with chapters?", "answer": "Use the report button on any chapter page to notify us of problems like missing pages or incorrect content."}, {"question": "Can I read manga offline?", "answer": "Currently, our platform requires an internet connection to read manga."}]}, "contact": {"title": "Still Need Help?", "content": "If you can't find the answer you're looking for, please don't hesitate to contact our support team."}}, "privacy": {"title": "Privacy Policy", "subtitle": "How we protect your information", "lastUpdated": "Last updated: {date}", "sections": [{"title": "Information We Collect", "content": "We collect information you provide directly to us, such as when you create an account, update your profile, or contact us for support."}, {"title": "How We Use Your Information", "content": "We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you."}, {"title": "Information Sharing", "content": "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy."}, {"title": "Data Security", "content": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction."}, {"title": "Your Rights", "content": "You have the right to access, update, or delete your personal information. You can manage your account settings or contact us for assistance."}]}, "terms": {"title": "Terms of Service", "subtitle": "Terms and conditions for using our service", "lastUpdated": "Last updated: {date}", "sections": [{"title": "Acceptance of Terms", "content": "By accessing and using this website, you accept and agree to be bound by the terms and provision of this agreement."}, {"title": "Use License", "content": "Permission is granted to temporarily download one copy of the materials on Manga Next's website for personal, non-commercial transitory viewing only."}, {"title": "Disclaimer", "content": "The materials on Manga Next's website are provided on an 'as is' basis. Manga Next makes no warranties, expressed or implied."}, {"title": "Limitations", "content": "In no event shall Manga Next or its suppliers be liable for any damages arising out of the use or inability to use the materials on this website."}, {"title": "User Conduct", "content": "Users must not use the service for any unlawful purpose or in any way that could damage, disable, or impair the service."}]}, "dmca": {"title": "DMCA Notice", "subtitle": "Digital Millennium Copyright Act compliance", "sections": [{"title": "Copyright Policy", "content": "Manga Next respects the intellectual property rights of others and expects users to do the same. We respond to notices of alleged copyright infringement that comply with the Digital Millennium Copyright Act."}, {"title": "Filing a DMCA Notice", "content": "If you believe that your copyrighted work has been copied in a way that constitutes copyright infringement, please provide our designated agent with the following information:"}, {"title": "Required Information", "items": ["A physical or electronic signature of the copyright owner", "Identification of the copyrighted work claimed to have been infringed", "Identification of the material that is claimed to be infringing", "Information reasonably sufficient to permit us to contact you", "A statement that you have a good faith belief that use of the material is not authorized", "A statement that the information in the notification is accurate"]}, {"title": "Contact Information", "content": "Please send DMCA notices to our designated agent via the contact form or email provided on our contact page."}]}, "contact": {"title": "Contact Us", "subtitle": "Get in touch with our team", "form": {"name": "Name", "email": "Email", "subject": "Subject", "message": "Message", "send": "Send Message", "sending": "Sending...", "success": "Message sent successfully!", "error": "Failed to send message. Please try again."}, "info": {"title": "Other Ways to Reach Us", "response": "We typically respond within 24-48 hours.", "business": "For business inquiries, please include detailed information about your request."}}, "common": {"backToHome": "Back to Home", "readMore": "Read More", "readLess": "Read Less"}}}