{"name": "manga-fake", "version": "0.1.0", "private": true, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev --turbopack", "build": "next build", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "seed": "npx ts-node prisma/seed.ts", "generate:icons": "node scripts/generate-icons.js", "generate:pwa-screenshots": "node scripts/generate-pwa-screenshots.js", "test:revalidation": "node scripts/test-revalidation.js", "test:view-stats": "node scripts/test-view-statistics.js", "test:cors": "node scripts/test-cors.js", "test:rankings": "node scripts/test-manga-rankings.js", "test:api-client": "node scripts/test-api-client-rankings.js", "test:db-migration": "node scripts/verify-database-migration.js", "test:pm2": "node scripts/test-pm2-config.js", "health:check": "node scripts/pm2-health-check.js check", "health:monitor": "node scripts/pm2-health-check.js monitor", "health:status": "node scripts/pm2-health-check.js status", "view-stats": "node scripts/view-stats-aggregation.js", "view-stats:comics": "node scripts/view-stats-aggregation.js comics", "view-stats:chapters": "node scripts/view-stats-aggregation.js chapters", "view-stats:snapshots": "node scripts/view-stats-aggregation.js snapshots", "view-stats:cleanup": "node scripts/view-stats-aggregation.js cleanup", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:status": "pm2 status", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "pm2:save": "pm2 save", "pm2:startup": "pm2 startup", "deploy:prod": "pnpm build && pnpm pm2:restart", "deploy:fresh": "pnpm install --frozen-lockfile && npx prisma generate && npx prisma migrate deploy && pnpm build && pnpm pm2:restart", "deploy:script": "./scripts/deploy-pm2.sh production deploy", "deploy:restart": "./scripts/deploy-pm2.sh production restart", "deploy:status": "./scripts/deploy-pm2.sh production status", "deploy:logs": "./scripts/deploy-pm2.sh production logs", "deploy:stop": "./scripts/deploy-pm2.sh production stop", "analyze": "pnpm build:analyze && open .next/bundle-analyzer-report.html", "optimize:images": "node scripts/optimize-images.js", "check:unused": "npx depcheck", "performance:test": "node scripts/performance-test.js", "crawler": "node src/scripts/crawler.js"}, "engines": {"node": ">=18.0.0", "pnpm": ">=6.35.1"}, "packageManager": "pnpm@10.7.0", "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "3.821.0", "@aws-sdk/s3-request-presigner": "3.821.0", "@hookform/resolvers": "^5.0.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.11", "@radix-ui/react-alert-dialog": "1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-radio-group": "1.3.7", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.7", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "cloudinary": "2.6.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "fs-extra": "^11.3.0", "lucide-react": "^0.509.0", "next": "15.3.2", "next-auth": "^4.24.11", "next-intl": "4.1.0", "next-pwa": "5.6.0", "next-themes": "^0.4.6", "p-limit": "^6.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "sharp": "^0.34.1", "sonner": "^2.0.3", "swr": "2.3.3", "tailwind-merge": "^3.2.0", "to-ico": "1.1.5", "zod": "^3.25.23"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.27.0", "@next/bundle-analyzer": "15.3.3", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4", "@types/bcrypt": "^5.0.2", "@types/fs-extra": "^11.0.4", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "depcheck": "^1.4.7", "eslint": "^9", "eslint-config-next": "15.3.2", "prettier": "3.5.3", "prisma": "^6.7.0", "tailwindcss": "^4", "ts-node": "^10.9.2", "tsx": "^4.20.0", "tw-animate-css": "^1.2.9", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}